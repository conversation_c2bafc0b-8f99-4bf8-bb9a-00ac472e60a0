"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import Pagination from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { api } from "@/utils/api"

// 从文件名提取原始文件名
const getOriginalFileName = (filePath: string) => {
  if (!filePath) return "Unknown File"

  // 从URL中提取文件名
  const urlParts = filePath.split("/")
  const fileName = urlParts[urlParts.length - 1]

  if (!fileName) return "Unknown File"

  // 移除URL参数
  const cleanFileName = fileName.split("?")[0]

  if (!cleanFileName) return "Unknown File"

  // URL解码文件名
  const decodedFileName = decodeURIComponent(cleanFileName)

  // 尝试提取原始文件名（移除时间戳等后缀）
  const match = decodedFileName.match(/^(.+?)_user_\d+_\d+\.(.+)$/)
  if (match) {
    return `${match[1]}.${match[2]}`
  }

  return decodedFileName
}

// Abstract submission data type from API
type AbstractSubmission = {
  abstract_id: number
  user_id: number
  title: string
  publication_status: string
  oral_presentation: boolean
  poster_presentation: boolean
  abstract_path: string
  filename?: string
  submit_time: string
  update_time: string
  theme: string
  id: number
  username: string
  name: string
  gender: string
  country: string
  organization: string
}

// Statistics data type
type AbstractStatistics = {
  total: number
  oral: number
  poster: number
}

// API response types
type ApiResponse<T> = {
  code: number
  msg: string
  data: T
}

export default function AdminAbstractsPage() {
  const [abstracts, setAbstracts] = useState<AbstractSubmission[]>([])
  const [statistics, setStatistics] = useState<AbstractStatistics>({ total: 0, oral: 0, poster: 0 })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [themeFilter, setThemeFilter] = useState<string>("all")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const itemsPerPage = 20

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const result = await api.get<
        | {
            code?: number
            data?: AbstractStatistics
            msg?: string
          }
        | AbstractStatistics
      >("/api/admin/abstract/statistics")

      let statisticsData: AbstractStatistics | null = null

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          statisticsData = result.data
        }
      } else if (result && typeof result === "object" && "total" in result) {
        // 处理直接返回数据的情况（向后兼容）
        statisticsData = result as AbstractStatistics
      }

      if (statisticsData) {
        setStatistics(statisticsData)
      }
    } catch (error) {
      console.error("Failed to fetch statistics:", error)
    }
  }

  // Fetch abstracts with pagination
  const fetchAbstracts = useCallback(async (page: number = 1, search: string = "", theme: string = "all") => {
    setLoading(true)
    try {
      const params = {
        limit: itemsPerPage.toString(),
        offset: ((page - 1) * itemsPerPage).toString(),
        search: search,
        theme: theme === "all" ? "" : theme,
      }
      const result = await api.get<
        | {
            code?: number
            data?: { abstracts: AbstractSubmission[]; total: number }
            msg?: string
          }
        | { abstracts: AbstractSubmission[]; total: number }
      >("/api/admin/abstracts", params)

      let abstractsData: { abstracts: AbstractSubmission[]; total: number } | null = null

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          abstractsData = result.data
        }
      } else if (result && typeof result === "object" && "abstracts" in result) {
        // 处理直接返回数据的情况（向后兼容）
        abstractsData = result as { abstracts: AbstractSubmission[]; total: number }
      }

      if (abstractsData) {
        setAbstracts(abstractsData.abstracts || [])
        setTotalCount(abstractsData.total || 0)
      } else {
        setAbstracts([])
        setTotalCount(0)
      }
    } catch (error) {
      console.error("Failed to fetch abstracts:", error)
      setAbstracts([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }, [itemsPerPage])

  useEffect(() => {
    fetchStatistics()
    fetchAbstracts(1, "", "all")
  }, [fetchAbstracts])

  // 处理搜索
  const handleSearch = () => {
    setCurrentPage(1)
    fetchAbstracts(1, searchTerm, themeFilter)
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchAbstracts(page, searchTerm, themeFilter)
  }

  // 处理主题筛选变化
  const handleThemeFilterChange = (theme: string) => {
    setThemeFilter(theme)
    setCurrentPage(1)
    fetchAbstracts(1, searchTerm, theme)
  }

  // 计算分页数据
  const totalPages = Math.ceil(totalCount / itemsPerPage)

  // 由于搜索和筛选在服务器端处理，直接使用返回的数据
  const filteredAbstracts = abstracts

  // Get presentation type display
  const getPresentationType = (oral: boolean, poster: boolean) => {
    if (oral && poster) return "Oral + Poster"
    if (oral) return "Oral Presentation"
    if (poster) return "Poster Presentation"
    return "Not Specified"
  }

  // Get publication status display
  const getPublicationStatus = (status: string) => {
    switch (status) {
      case "published":
        return "Published"
      case "unpublished":
        return "Unpublished"
      case "partially":
        return "Partially Published"
      default:
        return status
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abstract Submissions</h1>
          <p className="text-gray-600">View and download abstract submissions for IFMB 2025</p>
        </div>
        <Button>
          <i className="fas fa-download mr-2"></i>
          Export All
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Submissions</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{statistics.total}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-file-alt text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Poster Presentation</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{statistics.poster}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-image text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Oral Presentation</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{statistics.oral}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <i className="fas fa-microphone text-xl text-purple-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="flex-1">
              <Input
                placeholder="Search by title, author, or organization..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSearch} variant="outline">
                <i className="fas fa-search mr-2"></i>
                Search
              </Button>
              <Select value={themeFilter} onValueChange={handleThemeFilterChange}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="Filter by Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Themes</SelectItem>
                  <SelectItem value="Genetics and Genomics">Genetics and Genomics</SelectItem>
                  <SelectItem value="Development and Evolutionary Biology">
                    Development and Evolutionary Biology
                  </SelectItem>
                  <SelectItem value="Plant Physiology and Biochemistry">Plant Physiology and Biochemistry</SelectItem>
                  <SelectItem value="Breeding and Biotechnology">Breeding and Biotechnology</SelectItem>
                  <SelectItem value="Sustainable Agriculture">Sustainable Agriculture</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Abstract List */}
      <Card>
        <CardHeader>
          <CardTitle>Abstract Submissions ({totalCount} total, showing {filteredAbstracts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">Loading abstracts...</div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAbstracts.map((abstract) => (
                <div key={abstract.abstract_id} className="rounded-lg border border-gray-200 p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-2 flex items-center gap-2">
                        <h3 className="font-semibold text-gray-900">{abstract.title}</h3>
                        <Badge className="border-blue-200 bg-blue-100 text-blue-800">
                          {getPresentationType(abstract.oral_presentation, abstract.poster_presentation)}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 gap-2 text-sm text-gray-600 md:grid-cols-2">
                        <div>
                          <span className="font-medium">Author:</span> {abstract.name} ({abstract.username})
                        </div>
                        <div>
                          <span className="font-medium">Organization:</span> {abstract.organization}
                        </div>
                        <div>
                          <span className="font-medium">Theme:</span> {abstract.theme}
                        </div>
                        <div>
                          <span className="font-medium">Submission Date:</span>{" "}
                          {new Date(abstract.submit_time).toLocaleDateString()}
                        </div>
                        <div>
                          <span className="font-medium">Country:</span> {abstract.country}
                        </div>
                        <div>
                          <span className="font-medium">Publication Status:</span>{" "}
                          {getPublicationStatus(abstract.publication_status)}
                        </div>
                        <div className="col-span-2">
                          <span className="font-medium">File:</span>{" "}
                          {abstract.filename || getOriginalFileName(abstract.abstract_path)}
                        </div>
                      </div>
                    </div>
                    <div className="ml-4 flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          // abstract_path should already be a full URL from the backend
                          window.open(abstract.abstract_path, "_blank")
                        }}
                      >
                        <i className="fas fa-download mr-1"></i>
                        Download
                      </Button>
                      <Button size="sm" variant="outline">
                        <i className="fas fa-eye mr-1"></i>
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              {filteredAbstracts.length === 0 && !loading && (
                <div className="py-8 text-center text-gray-500">No abstracts found matching your criteria.</div>
              )}
            </div>
          )}

          {/* 分页组件 */}
          {totalPages > 1 && (
            <div className="mt-6 border-t pt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                itemsPerPage={itemsPerPage}
                totalItems={totalCount}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
