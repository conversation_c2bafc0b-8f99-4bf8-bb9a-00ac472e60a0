"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { <PERSON>ge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Pagination from "@/components/ui/pagination"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { AdminReservationData, AdminReservationsApiResponse } from "@/types/accommodation"
import { api } from "@/utils/api"

// Hotel data type from API
type Hotel = {
  hotel_id: number
  name: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
}

// Room type data from API
type RoomType = {
  room_id: number
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  checkin_date: string
  checkout_date: string
  hotel_id: number
  name: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
  Id: number
  room_type: string
}

export default function AdminHotelsPage() {
  const [hotels, setHotels] = useState<Hotel[]>([])
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([])
  const [reservations, setReservations] = useState<AdminReservationData[]>([])
  const [loading, setLoading] = useState(true)
  const [reservationsLoading, setReservationsLoading] = useState(false)
  const [isAddingHotel, setIsAddingHotel] = useState(false)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalReservations, setTotalReservations] = useState(0)
  const itemsPerPage = 10

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      const result = await api.get<
        | {
            code?: number
            data?: Hotel[]
            msg?: string
          }
        | Hotel[]
      >("/api/admin/accommodation/hotels")

      let hotelsData: Hotel[] = []

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          hotelsData = result.data
        }
      } else if (Array.isArray(result)) {
        // 处理直接返回数组的情况（向后兼容）
        hotelsData = result
      }

      setHotels(hotelsData)
    } catch (error) {
      console.error("Failed to fetch hotels:", error)
    }
  }

  // Fetch room types
  const fetchRoomTypes = async () => {
    try {
      const result = await api.get<
        | {
            code?: number
            data?: RoomType[]
            msg?: string
          }
        | RoomType[]
      >("/api/admin/accommodation/rooms")

      let roomTypesData: RoomType[] = []

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          roomTypesData = result.data
        }
      } else if (Array.isArray(result)) {
        // 处理直接返回数组的情况（向后兼容）
        roomTypesData = result
      }

      setRoomTypes(roomTypesData)
    } catch (error) {
      console.error("Failed to fetch room types:", error)
    } finally {
      setLoading(false)
    }
  }

  // Fetch reservations with pagination
  const fetchReservations = useCallback(async (page: number = 1) => {
    setReservationsLoading(true)
    try {
      const params = {
        limit: itemsPerPage.toString(),
        offset: ((page - 1) * itemsPerPage).toString(),
      }
      const result = await api.get<AdminReservationsApiResponse>("/api/admin/accommodation/reservations", params)

      let reservationsData: AdminReservationData[] = []
      let count = 0

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          reservationsData = result.data.list
          count = result.data.count || 0
        }
      }

      setReservations(reservationsData)
      setTotalReservations(count)
    } catch (error) {
      console.error("Failed to fetch reservations:", error)
      setReservations([])
      setTotalReservations(0)
    } finally {
      setReservationsLoading(false)
    }
  }, [itemsPerPage])

  useEffect(() => {
    fetchHotels()
    fetchRoomTypes()
  }, [])

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchReservations(page)
  }

  // 计算分页数据
  const totalPages = Math.ceil(totalReservations / itemsPerPage)

  // 新酒店表单数据
  const [newHotel, setNewHotel] = useState<Partial<Hotel>>({
    name: "",
    chinese_name: "",
    location: "",
    contact_phone: "",
  })

  // 添加酒店
  const handleAddHotel = () => {
    if (newHotel.name && newHotel.location) {
      // This would typically make an API call to add the hotel
      console.log("Adding hotel:", newHotel)
      setNewHotel({
        name: "",
        chinese_name: "",
        location: "",
        contact_phone: "",
      })
      setIsAddingHotel(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hotel Management</h1>
          <p className="text-gray-600">Manage hotels and room configurations for IFMB 2025</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsAddingHotel(true)}>
            <i className="fas fa-plus mr-2"></i>
            Add Hotel
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Hotels</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{hotels.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-hotel text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Room Types</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{roomTypes.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-bed text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Rooms</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">
                  {roomTypes.reduce((sum, rt) => sum + rt.total, 0)}
                </h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <i className="fas fa-door-open text-xl text-purple-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Available Rooms</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">
                  {roomTypes.reduce((sum, rt) => sum + (rt.total - rt.obligate), 0)}
                </h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                <i className="fas fa-key text-xl text-orange-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容标签页 */}
      <Tabs
        defaultValue="hotels"
        className="w-full"
        onValueChange={(value) => {
          if (value === "bookings" && reservations.length === 0) {
            fetchReservations(1)
          }
        }}
      >
        <TabsList className="mb-4">
          <TabsTrigger value="hotels">Hotels</TabsTrigger>
          <TabsTrigger value="rooms">Room Types</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="merging">Room Merging</TabsTrigger>
        </TabsList>

        {/* 酒店管理 */}
        <TabsContent value="hotels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hotel List</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Loading hotels...</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {hotels.map((hotel) => (
                    <div key={hotel.hotel_id} className="rounded-lg border border-gray-200 p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="mb-2 flex items-center gap-2">
                            <h3 className="font-semibold text-gray-900">{hotel.name}</h3>
                            <Badge className="bg-green-100 text-green-800">Active</Badge>
                          </div>
                          <p className="mb-1 text-sm text-gray-600">{hotel.chinese_name}</p>
                          <p className="mb-2 text-sm text-gray-600">{hotel.location}</p>
                          <div className="mb-2 text-sm text-gray-600">
                            <span className="font-medium">Contact:</span> {hotel.contact_phone}
                          </div>
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Check-in:</span>{" "}
                            {new Date(hotel.default_checkin_date).toLocaleDateString()} -{" "}
                            <span className="font-medium">Check-out:</span>{" "}
                            {new Date(hotel.default_checkout_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="ml-4 flex gap-2">
                          <Button size="sm" variant="outline">
                            <i className="fas fa-edit mr-1"></i>
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            <i className="fas fa-eye mr-1"></i>
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {hotels.length === 0 && !loading && (
                    <div className="py-8 text-center text-gray-500">No hotels found.</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 房间类型管理 */}
        <TabsContent value="rooms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Types</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Loading room types...</div>
                </div>
              ) : (
                <div className="space-y-4">
                  {roomTypes.map((roomType) => (
                    <div key={roomType.room_id} className="rounded-lg border border-gray-200 p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="mb-2 flex items-center gap-2">
                            <h3 className="font-semibold text-gray-900">{roomType.room_type || roomType.type}</h3>
                            <Badge variant="outline">{roomType.name}</Badge>
                          </div>
                          <p className="mb-2 text-sm text-gray-600">{roomType.chinese_name}</p>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 md:grid-cols-4">
                            <div>
                              <span className="font-medium">Price:</span> ¥{roomType.price}/night
                            </div>
                            <div>
                              <span className="font-medium">Total Rooms:</span> {roomType.total}
                            </div>
                            <div>
                              <span className="font-medium">Obligated:</span> {roomType.obligate}
                            </div>
                            <div>
                              <span className="font-medium">Available:</span> {roomType.total - roomType.obligate}
                            </div>
                          </div>
                          <div className="mt-2 text-sm text-gray-600">
                            <span className="font-medium">Check-in:</span>{" "}
                            {new Date(roomType.checkin_date).toLocaleDateString()} -{" "}
                            <span className="font-medium">Check-out:</span>{" "}
                            {new Date(roomType.checkout_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="ml-4 flex gap-2">
                          <Button size="sm" variant="outline">
                            <i className="fas fa-edit mr-1"></i>
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            <i className="fas fa-chart-bar mr-1"></i>
                            Stats
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {roomTypes.length === 0 && !loading && (
                    <div className="py-8 text-center text-gray-500">No room types found.</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 预订管理 */}
        <TabsContent value="bookings" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Room Bookings</CardTitle>
                <Button onClick={() => fetchReservations(currentPage)} disabled={reservationsLoading}>
                  <i className={`fas fa-sync-alt mr-2 ${reservationsLoading ? "animate-spin" : ""}`}></i>
                  {reservationsLoading ? "Loading..." : "Refresh"}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {reservationsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Loading reservations...</div>
                </div>
              ) : reservations.length === 0 ? (
                <div className="py-8 text-center">
                  <i className="fas fa-calendar-check mb-4 text-4xl text-gray-400"></i>
                  <h3 className="mb-2 text-lg font-medium text-gray-900">No Reservations Found</h3>
                  <p className="mb-4 text-gray-600">No room bookings have been made yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="mb-4 text-sm text-gray-600">
                    Total reservations: {totalReservations} (showing {reservations.length} on page {currentPage})
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                            Guest Info
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                            Hotel & Room
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                            Dates
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                            Sharing
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                            Price
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 bg-white">
                        {reservations.map((reservation, index) => (
                          <tr key={`${reservation.occupant}-${index}`} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{reservation.username}</div>
                                  <div className="text-sm text-gray-500">
                                    {reservation.gender} • {reservation.country}
                                  </div>
                                  <div className="text-sm text-gray-500">{reservation.organization}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">{reservation.chinese_name}</div>
                              <div className="text-sm text-gray-500">{reservation.room_type}</div>
                              <div className="text-sm text-gray-500">Room ID: {reservation.room_id}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {new Date(reservation.checkin_date).toLocaleDateString()}
                              </div>
                              <div className="text-sm text-gray-500">
                                to {new Date(reservation.checkout_date).toLocaleDateString()}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge
                                className={
                                  reservation.shared_option === "none"
                                    ? "bg-blue-100 text-blue-800"
                                    : reservation.shared_option === "with partner"
                                      ? "bg-green-100 text-green-800"
                                      : "bg-yellow-100 text-yellow-800"
                                }
                              >
                                {reservation.shared_option}
                              </Badge>
                              {reservation.roommate.username && (
                                <div className="mt-1 text-xs text-gray-500">
                                  Roommate: {reservation.roommate.username}
                                </div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge
                                className={
                                  reservation.is_assigned
                                    ? "bg-green-100 text-green-800"
                                    : "bg-yellow-100 text-yellow-800"
                                }
                              >
                                {reservation.is_assigned ? "Assigned" : "Pending"}
                              </Badge>
                              {reservation.AssignedRoomId > 0 && (
                                <div className="mt-1 text-xs text-gray-500">
                                  Assigned Room: {reservation.AssignedRoomId}
                                </div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">¥{reservation.price}</div>
                              <div className="text-sm text-gray-500">by {reservation.executor_username}</div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* 分页组件 */}
              {totalPages > 1 && (
                <div className="mt-6 border-t pt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    itemsPerPage={itemsPerPage}
                    totalItems={totalReservations}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 房间合并 */}
        <TabsContent value="merging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Merging</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <i className="fas fa-object-group mb-4 text-4xl text-gray-400"></i>
                <h3 className="mb-2 text-lg font-medium text-gray-900">Room Merging Management</h3>
                <p className="mb-4 text-gray-600">Manage room sharing and merging requests</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加酒店对话框 */}
      {isAddingHotel && (
        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
          <div className="w-full max-w-md rounded-lg bg-white p-6">
            <h3 className="mb-4 text-lg font-semibold">Add New Hotel</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="hotel-name">Hotel Name</Label>
                <Input
                  id="hotel-name"
                  value={newHotel.name}
                  onChange={(e) => setNewHotel({ ...newHotel, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-chinese-name">Chinese Name</Label>
                <Input
                  id="hotel-chinese-name"
                  value={newHotel.chinese_name}
                  onChange={(e) => setNewHotel({ ...newHotel, chinese_name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-location">Location</Label>
                <Textarea
                  id="hotel-location"
                  value={newHotel.location}
                  onChange={(e) => setNewHotel({ ...newHotel, location: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-phone">Contact Phone</Label>
                <Input
                  id="hotel-phone"
                  value={newHotel.contact_phone}
                  onChange={(e) => setNewHotel({ ...newHotel, contact_phone: e.target.value })}
                />
              </div>
            </div>
            <div className="mt-6 flex gap-2">
              <Button onClick={handleAddHotel}>Add Hotel</Button>
              <Button variant="outline" onClick={() => setIsAddingHotel(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
